package com.collabhub.be;

import com.collabhub.be.modules.collaborationhub.service.BriefAccessControlService;
import com.collabhub.be.modules.collaborationhub.service.CollabHubPermissionService;
import com.collabhub.be.modules.collaborationhub.service.CollabHubPermissionService;
import com.collabhub.be.modules.posts.service.PostPermissionService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * Simple test to verify that all permission services can be compiled and loaded.
 */
@SpringBootTest
public class CompilationTest {

    @Test
    public void testPermissionServicesExist() {
        // This test will fail to compile if any of the services are missing
        Class<?> briefAccessControlService = BriefAccessControlService.class;
        Class<?> collabHubPermissionService = CollabHubPermissionService.class;
        Class<?> hubParticipantPermissionService = HubParticipantPermissionService.class;
        Class<?> postPermissionService = PostPermissionService.class;
        
        // If we get here, all services exist and can be compiled
        assert briefAccessControlService != null;
        assert collabHubPermissionService != null;
        assert hubParticipantPermissionService != null;
        assert postPermissionService != null;
    }
}
