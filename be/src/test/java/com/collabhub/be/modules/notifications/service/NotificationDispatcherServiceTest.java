package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.notifications.dto.NotificationMetadata;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import com.collabhub.be.modules.notifications.dto.NotificationUrgency;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.tables.pojos.User;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for NotificationDispatcherService focusing on the new hub participant-based methods.
 */
@ExtendWith(MockitoExtension.class)
class NotificationDispatcherServiceTest {

    @Mock
    private NotificationService notificationService;
    
    @Mock
    private NotificationStorageService notificationStorageService;
    
    @Mock
    private EmailNotificationService emailNotificationService;
    
    @Mock
    private NotificationBatchingService notificationBatchingService;
    
    @Mock
    private com.collabhub.be.modules.auth.repository.UserRepository userRepository;
    
    @Mock
    private NotificationPreferenceService notificationPreferenceService;
    
    @Mock
    private ExternalEmailNotificationService externalEmailNotificationService;

    @InjectMocks
    private NotificationDispatcherService notificationDispatcherService;

    @Test
    void shouldDispatchToHubParticipants_WithMixedInternalAndExternalUsers() {
        // Given
        HubParticipant internalParticipant = createInternalParticipant(1L, 123L, "<EMAIL>", "Internal User");
        HubParticipant externalParticipant = createExternalParticipant(2L, "<EMAIL>", "External User");
        List<HubParticipant> participants = List.of(internalParticipant, externalParticipant);
        
        User internalUser = createUser(123L, "<EMAIL>", "Internal User");
        when(userRepository.findByIds(List.of(123L))).thenReturn(List.of(internalUser));
        
        // Mock notification service for internal users
        NotificationService.NotificationDeliveryPlan deliveryPlan = new NotificationService.NotificationDeliveryPlan();
        deliveryPlan.addInAppRecipient(123L);
        deliveryPlan.addEmailRecipient(123L);
        when(notificationService.processNotificationEvent(any(), anyList())).thenReturn(deliveryPlan);
        
        // Mock external user preferences
        when(notificationPreferenceService.isExternalNotificationEnabled(
            "<EMAIL>", NotificationType.COMMENT_ADDED, 
            com.collabhub.be.modules.notifications.dto.NotificationChannel.EMAIL))
            .thenReturn(true);

        // When
        notificationDispatcherService.dispatchToHubParticipants(
            NotificationType.COMMENT_ADDED,
            "Test Notification",
            "Test message",
            participants,
            null,
            NotificationMetadata.empty()
        );

        // Then
        verify(notificationStorageService).createNotifications(any(), any(), any(), any(), any(), any());
        verify(notificationBatchingService).queueNotificationsForBatching(any(), any(), any(), any(), any(), any(), any());
        verify(notificationBatchingService).queueExternalNotificationsForBatching(any(), any(), any(), anyList(), any(), any(), any());
    }

    @Test
    void shouldDispatchToUserIds_BackwardCompatibility() {
        // Given
        List<Long> userIds = List.of(123L, 456L);
        User user1 = createUser(123L, "<EMAIL>", "User 1");
        User user2 = createUser(456L, "<EMAIL>", "User 2");
        when(userRepository.findByIds(userIds)).thenReturn(List.of(user1, user2));
        
        // Mock notification service
        NotificationService.NotificationDeliveryPlan deliveryPlan = new NotificationService.NotificationDeliveryPlan();
        deliveryPlan.addInAppRecipient(123L);
        deliveryPlan.addEmailRecipient(456L);
        when(notificationService.processNotificationEvent(any(), anyList())).thenReturn(deliveryPlan);

        // When
        notificationDispatcherService.dispatchToUserIds(
            NotificationType.INVITE_TO_HUB,
            "Test Notification",
            "Test message",
            userIds,
            null,
            NotificationMetadata.empty(),
            NotificationUrgency.NORMAL
        );

        // Then
        verify(notificationStorageService).createNotifications(any(), any(), any(), any(), any(), any());
        verify(notificationBatchingService).queueNotificationsForBatching(any(), any(), any(), any(), any(), any(), any());
    }

    private HubParticipant createInternalParticipant(Long id, Long userId, String email, String name) {
        HubParticipant participant = new HubParticipant();
        participant.setId(id);
        participant.setUserId(userId);
        participant.setEmail(email);
        participant.setName(name);
        participant.setIsExternal(false);
        return participant;
    }

    private HubParticipant createExternalParticipant(Long id, String email, String name) {
        HubParticipant participant = new HubParticipant();
        participant.setId(id);
        participant.setUserId(null);
        participant.setEmail(email);
        participant.setName(name);
        participant.setIsExternal(true);
        return participant;
    }

    private User createUser(Long id, String email, String name) {
        User user = new User();
        user.setId(id);
        user.setEmail(email);
        user.setDisplayName(name);
        return user;
    }
}
